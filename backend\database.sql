-- School Management Database Setup
-- Run these commands in your MySQL server

-- Create database
CREATE DATABASE IF NOT EXISTS school_management;
USE school_management;

-- Create schools table
CREATE TABLE IF NOT EXISTS schools (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  address VARCHAR(500) NOT NULL,
  latitude FLOAT NOT NULL,
  longitude FLOAT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample data (optional)
INSERT INTO schools (name, address, latitude, longitude) VALUES
('Springfield Elementary School', '123 Main St, Springfield', 40.7128, -74.0060),
('Riverside High School', '456 Oak Ave, Riverside', 40.7589, -73.9851),
('Greenwood Middle School', '789 Pine Rd, Greenwood', 40.7831, -73.9712),
('Sunset Academy', '321 Elm St, Sunset', 40.7505, -73.9934),
('Maple Leaf Elementary', '654 Maple Dr, Maplewood', 40.7282, -74.0776);

-- Verify table creation
DESCRIBE schools;

-- Show sample data
SELECT * FROM schools;
