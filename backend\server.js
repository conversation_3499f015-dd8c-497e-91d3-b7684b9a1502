const express = require('express');
const cors = require('cors');
const Joi = require('joi');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// In-memory database with JSON file persistence
const DB_FILE = path.join(__dirname, 'schools_data.json');

// Initialize database with sample data
let schools = [
  {
    id: 1,
    name: 'Springfield Elementary School',
    address: '123 Main St, Springfield',
    latitude: 40.7128,
    longitude: -74.0060,
    created_at: new Date().toISOString()
  },
  {
    id: 2,
    name: 'Riverside High School',
    address: '456 Oak Ave, Riverside',
    latitude: 40.7589,
    longitude: -73.9851,
    created_at: new Date().toISOString()
  },
  {
    id: 3,
    name: 'Greenwood Middle School',
    address: '789 Pine Rd, Greenwood',
    latitude: 40.7831,
    longitude: -73.9712,
    created_at: new Date().toISOString()
  },
  {
    id: 4,
    name: 'Sunset Academy',
    address: '321 Elm St, Sunset',
    latitude: 40.7505,
    longitude: -73.9934,
    created_at: new Date().toISOString()
  },
  {
    id: 5,
    name: 'Maple Leaf Elementary',
    address: '654 Maple Dr, Maplewood',
    latitude: 40.7282,
    longitude: -74.0776,
    created_at: new Date().toISOString()
  }
];

// Load existing data if file exists
function loadSchoolsFromFile() {
  try {
    if (fs.existsSync(DB_FILE)) {
      const data = fs.readFileSync(DB_FILE, 'utf8');
      const loadedSchools = JSON.parse(data);
      if (Array.isArray(loadedSchools) && loadedSchools.length > 0) {
        schools = loadedSchools;
        console.log(`Loaded ${schools.length} schools from file`);
      }
    }
  } catch (error) {
    console.log('Using default sample data');
  }
}

// Save schools to file
function saveSchoolsToFile() {
  try {
    fs.writeFileSync(DB_FILE, JSON.stringify(schools, null, 2));
  } catch (error) {
    console.error('Error saving schools to file:', error);
  }
}

// Initialize data
loadSchoolsFromFile();

// Validation schema
const schoolSchema = Joi.object({
  name: Joi.string().min(1).max(255).required(),
  address: Joi.string().min(1).max(500).required(),
  latitude: Joi.number().min(-90).max(90).required(),
  longitude: Joi.number().min(-180).max(180).required()
});

// Helper function to calculate distance
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // Radius of Earth in kilometers
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const d = R * c;
  return Math.round(d * 100) / 100;
}

function deg2rad(deg) {
  return deg * (Math.PI/180);
}

// POST /addSchool
app.post('/addSchool', async (req, res) => {
  try {
    console.log('Received request to add school:', req.body);

    const { error, value } = schoolSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        details: error.details[0].message
      });
    }

    const { name, address, latitude, longitude } = value;

    // Generate new ID
    const newId = schools.length > 0 ? Math.max(...schools.map(s => s.id)) + 1 : 1;

    // Create new school object
    const newSchool = {
      id: newId,
      name,
      address,
      latitude,
      longitude,
      created_at: new Date().toISOString()
    };

    // Add to in-memory database
    schools.push(newSchool);

    // Save to file for persistence
    saveSchoolsToFile();

    const response = {
      id: newSchool.id,
      name,
      address,
      latitude,
      longitude
    };

    console.log('School added successfully:', response);
    res.status(201).json(response);

  } catch (error) {
    console.error('Error adding school:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// GET /listSchools
app.get('/listSchools', async (req, res) => {
  try {
    console.log('Received request to list schools:', req.query);

    const { latitude, longitude } = req.query;
    const lat = parseFloat(latitude);
    const lng = parseFloat(longitude);

    if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
      return res.status(400).json({
        success: false,
        message: 'Invalid latitude or longitude'
      });
    }

    // Calculate distance for each school and sort by proximity
    const schoolsWithDistance = schools.map(school => ({
      ...school,
      distance: calculateDistance(lat, lng, school.latitude, school.longitude)
    })).sort((a, b) => a.distance - b.distance);

    console.log('Schools fetched successfully:', schoolsWithDistance.length, 'schools');
    res.json(schoolsWithDistance);

  } catch (error) {
    console.error('Error fetching schools:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
