// Simple API test script
// Run this after starting the server to test the endpoints

const testAPI = async () => {
  const baseURL = 'http://localhost:3000';
  
  console.log('🧪 Testing School Management API...\n');
  
  try {
    // Test 1: Add a school
    console.log('1️⃣ Testing POST /addSchool');
    const schoolData = {
      name: 'Test Elementary School',
      address: '123 Test Street, Test City',
      latitude: 40.7128,
      longitude: -74.0060
    };
    
    const addResponse = await fetch(`${baseURL}/addSchool`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(schoolData),
    });
    
    if (addResponse.ok) {
      const addResult = await addResponse.json();
      console.log('✅ School added successfully:', addResult);
    } else {
      console.log('❌ Failed to add school:', addResponse.status);
    }
    
    // Test 2: List schools
    console.log('\n2️⃣ Testing GET /listSchools');
    const listResponse = await fetch(`${baseURL}/listSchools?latitude=40.7589&longitude=-73.9851`);
    
    if (listResponse.ok) {
      const schools = await listResponse.json();
      console.log('✅ Schools retrieved successfully:');
      console.log(`   Found ${schools.length} schools`);
      if (schools.length > 0) {
        console.log('   First school:', schools[0]);
      }
    } else {
      console.log('❌ Failed to list schools:', listResponse.status);
    }
    
    console.log('\n🎉 API tests completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n💡 Make sure the server is running: npm start');
  }
};

// Run tests if this file is executed directly
if (require.main === module) {
  testAPI();
}

module.exports = testAPI;
