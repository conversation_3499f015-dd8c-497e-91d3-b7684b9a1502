{"name": "school-management-backend", "version": "1.0.0", "description": "Backend for school management system", "main": "server.js", "type": "commonjs", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node test-api.js"}, "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "joi": "^17.9.2"}, "devDependencies": {"nodemon": "^3.0.1"}}