
import { AddSchoolRequest, ListSchoolsParams, School } from "@/types/school";

// Configuration for your Node.js backend
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

class SchoolApiService {
  async addSchool(schoolData: AddSchoolRequest): Promise<School> {
    console.log(`Making request to: ${API_BASE_URL}/addSchool`);
    console.log('Request payload:', schoolData);
    
    const response = await fetch(`${API_BASE_URL}/addSchool`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(schoolData),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Server response:', response.status, errorText);
      throw new Error(`Failed to add school: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('Server response:', result);
    return result;
  }

  async listSchools(params: ListSchoolsParams): Promise<School[]> {
    const queryParams = new URLSearchParams({
      latitude: params.latitude.toString(),
      longitude: params.longitude.toString(),
    });

    console.log(`Making request to: ${API_BASE_URL}/listSchools?${queryParams}`);
    
    const response = await fetch(`${API_BASE_URL}/listSchools?${queryParams}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Server response:', response.status, errorText);
      throw new Error(`Failed to fetch schools: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('Server response:', result);
    return result;
  }

  // Helper method to calculate distance between two coordinates
  calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const d = R * c; // Distance in kilometers
    return Math.round(d * 100) / 100; // Round to 2 decimal places
  }

  private deg2rad(deg: number): number {
    return deg * (Math.PI / 180);
  }
}

export const schoolApiService = new SchoolApiService();
