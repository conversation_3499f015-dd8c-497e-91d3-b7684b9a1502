
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { MapPin, Search, AlertCircle, School as SchoolIcon, Navigation } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { schoolApiService } from "@/services/schoolApi";
import { School } from "@/types/school";
import LocationHandler from "./LocationHandler";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface SchoolsListProps {
  schools: School[];
  userLocation: { lat: number; lng: number } | null;
  onLocationUpdate: (location: { lat: number; lng: number }) => void;
}

const SchoolsList = ({ schools: initialSchools, userLocation, onLocationUpdate }: SchoolsListProps) => {
  const [schools, setSchools] = useState<School[]>(initialSchools);
  const [userCoords, setUserCoords] = useState({
    latitude: userLocation?.lat?.toString() || "",
    longitude: userLocation?.lng?.toString() || "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    setSchools(initialSchools);
  }, [initialSchools]);

  useEffect(() => {
    if (userLocation) {
      setUserCoords({
        latitude: userLocation.lat.toString(),
        longitude: userLocation.lng.toString(),
      });
    }
  }, [userLocation]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setUserCoords(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleLocationUpdate = (lat: number, lng: number) => {
    setUserCoords({
      latitude: lat.toString(),
      longitude: lng.toString(),
    });
    onLocationUpdate({ lat, lng });
  };

  const fetchSortedSchools = async () => {
    const lat = parseFloat(userCoords.latitude);
    const lng = parseFloat(userCoords.longitude);

    if (isNaN(lat) || isNaN(lng)) {
      toast({
        title: "Invalid Coordinates",
        description: "Please enter valid latitude and longitude or use current location",
        variant: "destructive",
      });
      return;
    }

    if (lat < -90 || lat > 90) {
      toast({
        title: "Invalid Latitude",
        description: "Latitude must be between -90 and 90",
        variant: "destructive",
      });
      return;
    }

    if (lng < -180 || lng > 180) {
      toast({
        title: "Invalid Longitude", 
        description: "Longitude must be between -180 and 180",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      console.log("Fetching schools for coordinates:", { lat, lng });
      const sortedSchools = await schoolApiService.listSchools({
        latitude: lat,
        longitude: lng,
      });
      
      setSchools(sortedSchools);
      onLocationUpdate({ lat, lng });
      
      toast({
        title: "Schools Updated!",
        description: `Found ${sortedSchools.length} schools sorted by proximity`,
      });
    } catch (error: any) {
      console.error("Failed to fetch schools:", error);
      
      toast({
        title: "Connection Error",
        description: "Could not connect to the server. Please ensure the backend is running on http://localhost:3001",
        variant: "destructive",
      });
      
      // Fallback: calculate distances locally if we have schools
      if (initialSchools.length > 0) {
        const schoolsWithDistance = initialSchools.map(school => ({
          ...school,
          distance: schoolApiService.calculateDistance(lat, lng, school.latitude, school.longitude)
        })).sort((a, b) => (a.distance || 0) - (b.distance || 0));
        
        setSchools(schoolsWithDistance);
        
        toast({
          title: "Offline Mode",
          description: "Showing local schools with calculated distances",
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Alert className="border-blue-200 bg-blue-50">
        <AlertCircle className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          <strong>Backend Connected:</strong> Node.js server running on http://localhost:3001 with in-memory database ready!
        </AlertDescription>
      </Alert>

      <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50">
        <CardHeader className="bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-t-lg">
          <CardTitle className="flex items-center gap-2 text-xl">
            <Navigation className="h-6 w-6" />
            Find Nearby Schools
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="user-latitude" className="text-sm font-medium text-gray-700">
                Your Latitude
              </Label>
              <Input
                id="user-latitude"
                name="latitude"
                type="number"
                step="any"
                value={userCoords.latitude}
                onChange={handleInputChange}
                placeholder="e.g., 40.7128"
                className="border-gray-300 focus:border-purple-500 focus:ring-purple-500"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="user-longitude" className="text-sm font-medium text-gray-700">
                Your Longitude
              </Label>
              <Input
                id="user-longitude"
                name="longitude"
                type="number"
                step="any"
                value={userCoords.longitude}
                onChange={handleInputChange}
                placeholder="e.g., -74.0060"
                className="border-gray-300 focus:border-purple-500 focus:ring-purple-500"
              />
            </div>
          </div>

          <div className="border-t pt-6">
            <h4 className="text-sm font-medium text-gray-700 mb-4 flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Get Current Location
            </h4>
            <LocationHandler 
              onLocationUpdate={handleLocationUpdate}
              currentLocation={userLocation}
            />
          </div>

          <div className="pt-4">
            <Button
              onClick={fetchSortedSchools}
              disabled={isLoading || !userCoords.latitude || !userCoords.longitude}
              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium py-3 text-lg"
            >
              <Search className="h-5 w-5 mr-2" />
              {isLoading ? "Searching..." : "Find Nearby Schools"}
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <SchoolIcon className="h-6 w-6" />
            Schools {schools.length > 0 && userLocation ? "(Sorted by Proximity)" : ""}
          </h3>
          {schools.length > 0 && (
            <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
              {schools.length} school{schools.length !== 1 ? 's' : ''} found
            </span>
          )}
        </div>
        
        {schools.length === 0 ? (
          <Card className="border-2 border-dashed border-300 bg-gray-50">
            <CardContent className="pt-8 pb-8 text-center">
              <SchoolIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-lg text-gray-600 mb-2">No schools found</p>
              <p className="text-sm text-gray-500">Add some schools first or check your backend connection!</p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4">
            {schools.map((school, index) => (
              <Card key={school.id || index} className="hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500 bg-white">
                <CardContent className="p-6">
                  <div className="flex justify-between items-start">
                    <div className="space-y-3 flex-1">
                      <div className="flex items-start gap-3">
                        <div className="bg-blue-100 p-2 rounded-full">
                          <SchoolIcon className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-bold text-xl text-gray-900 mb-1">{school.name}</h4>
                          <p className="text-gray-600 flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-gray-400" />
                            {school.address}
                          </p>
                        </div>
                      </div>
                      <div className="bg-gray-50 p-3 rounded-md">
                        <p className="text-sm text-gray-600">
                          <strong>Coordinates:</strong> {school.latitude.toFixed(6)}, {school.longitude.toFixed(6)}
                        </p>
                      </div>
                    </div>
                    
                    {school.distance !== undefined && (
                      <div className="ml-4 text-right">
                        <div className="bg-green-100 px-4 py-2 rounded-full">
                          <p className="text-lg font-bold text-green-700">
                            {school.distance} km
                          </p>
                          <p className="text-xs text-green-600">away</p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SchoolsList;
