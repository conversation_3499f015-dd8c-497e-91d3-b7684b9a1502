# 🚀 Backend Setup Guide

## Quick Start

Your Node.js backend server is now ready! Follow these steps to get it running:

### Step 1: Database Setup
1. **Start MySQL Server** (make sure MySQL is running on your system)
2. **Run the database setup script:**
   ```sql
   -- Open MySQL command line or MySQL Workbench and run:
   source backend/database.sql
   
   -- OR copy and paste the contents of backend/database.sql
   ```

### Step 2: Configure Database Connection
1. **Edit `backend/.env` file** with your MySQL credentials:
   ```env
   DB_HOST=localhost
   DB_USER=root
   DB_PASSWORD=your_mysql_password
   DB_NAME=school_management
   PORT=3000
   ```

### Step 3: Start the Backend Server
```bash
# Navigate to backend directory
cd backend

# Start the server
npm start

# OR for development with auto-restart
npm run dev
```

### Step 4: Verify Backend is Running
- Open your browser and go to: `http://localhost:3000`
- You should see the server running
- The frontend will automatically connect to this backend

## 🎯 What's Included

✅ **Express.js Server** - Fast, minimalist web framework  
✅ **MySQL Integration** - Database connection with mysql2  
✅ **CORS Enabled** - Cross-origin requests for frontend  
✅ **Input Validation** - Joi validation for API requests  
✅ **Distance Calculation** - Haversine formula for proximity sorting  
✅ **Error Handling** - Comprehensive error responses  
✅ **Environment Configuration** - Flexible .env setup  

## 📡 API Endpoints

### POST /addSchool
Add a new school to the database
```bash
curl -X POST http://localhost:3000/addSchool \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test School",
    "address": "123 Test St",
    "latitude": 40.7128,
    "longitude": -74.0060
  }'
```

### GET /listSchools
Get schools sorted by distance
```bash
curl "http://localhost:3000/listSchools?latitude=40.7589&longitude=-73.9851"
```

## 🔧 Troubleshooting

**Database Connection Error?**
- Check if MySQL is running
- Verify credentials in `backend/.env`
- Make sure database `school_management` exists

**Port 3000 already in use?**
- Change PORT in `backend/.env` to another port (e.g., 3001)
- Update frontend API URL if needed

**Frontend can't connect?**
- Make sure backend is running on http://localhost:3000
- Check browser console for CORS errors
- Verify both frontend and backend are running

## 🎉 Success!

Once the backend is running:
1. Your React frontend will automatically connect
2. You can add schools through the web interface
3. Schools will be stored in MySQL database
4. Distance calculations will work in real-time

The backend is now fully integrated with your existing React frontend!
