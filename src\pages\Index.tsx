
import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { School, MapPin, Book, Server } from "lucide-react";
import AddSchoolForm from "@/components/AddSchoolForm";
import SchoolsList from "@/components/SchoolsList";
import BackendDocumentation from "@/components/BackendDocumentation";
import { School as SchoolType } from "@/types/school";

const Index = () => {
  const [schools, setSchools] = useState<SchoolType[]>([]);
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);

  const handleSchoolAdded = (newSchool: SchoolType) => {
    setSchools(prev => [...prev, newSchool]);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-3 rounded-full">
              <School className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              School Management System
            </h1>
          </div>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Add schools to your database and discover them by proximity to any location with our modern school management platform
          </p>
        </div>

        <Tabs defaultValue="add-school" className="w-full max-w-6xl mx-auto">
          <TabsList className="grid w-full grid-cols-3 mb-8 bg-white shadow-lg border-0 h-14">
            <TabsTrigger 
              value="add-school" 
              className="flex items-center gap-2 text-lg font-medium data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white"
            >
              <School className="h-5 w-5" />
              Add School
            </TabsTrigger>
            <TabsTrigger 
              value="schools-list" 
              className="flex items-center gap-2 text-lg font-medium data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-pink-600 data-[state=active]:text-white"
            >
              <MapPin className="h-5 w-5" />
              Find Schools
            </TabsTrigger>
            <TabsTrigger 
              value="backend-docs" 
              className="flex items-center gap-2 text-lg font-medium data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-600 data-[state=active]:text-white"
            >
              <Server className="h-5 w-5" />
              Backend Setup
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="add-school" className="space-y-6">
            <Card className="border-0 shadow-2xl bg-white/80 backdrop-blur-sm">
              <CardHeader className="text-center pb-2">
                <CardTitle className="text-2xl font-bold text-gray-900 flex items-center justify-center gap-2">
                  <Book className="h-6 w-6 text-blue-600" />
                  Add New School
                </CardTitle>
                <CardDescription className="text-lg text-gray-600">
                  Enter school details to add it to your database
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AddSchoolForm onSchoolAdded={handleSchoolAdded} />
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="schools-list" className="space-y-6">
            <Card className="border-0 shadow-2xl bg-white/80 backdrop-blur-sm">
              <CardHeader className="text-center pb-2">
                <CardTitle className="text-2xl font-bold text-gray-900 flex items-center justify-center gap-2">
                  <MapPin className="h-6 w-6 text-purple-600" />
                  Discover Schools
                </CardTitle>
                <CardDescription className="text-lg text-gray-600">
                  Find schools near any location, sorted by proximity
                </CardDescription>
              </CardHeader>
              <CardContent>
                <SchoolsList 
                  schools={schools}
                  userLocation={userLocation}
                  onLocationUpdate={setUserLocation}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="backend-docs" className="space-y-6">
            <Card className="border-0 shadow-2xl bg-white/80 backdrop-blur-sm">
              <CardHeader className="text-center pb-2">
                <CardTitle className="text-2xl font-bold text-gray-900 flex items-center justify-center gap-2">
                  <Server className="h-6 w-6 text-green-600" />
                  Backend Development Guide
                </CardTitle>
                <CardDescription className="text-lg text-gray-600">
                  Complete Node.js API implementation guide
                </CardDescription>
              </CardHeader>
              <CardContent>
                <BackendDocumentation />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Index;
