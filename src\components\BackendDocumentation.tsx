
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Code, Database, Server, Globe } from "lucide-react";

const BackendDocumentation = () => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Server className="h-5 w-5" />
            Node.js Backend Implementation Guide
          </CardTitle>
          <CardDescription>
            Complete guide to implement the school management backend APIs
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          
          <div>
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <Database className="h-5 w-5" />
              Database Setup
            </h3>
            <div className="bg-gray-100 p-4 rounded-lg">
              <pre className="text-sm overflow-x-auto">
{`-- Create MySQL database and schools table
CREATE DATABASE school_management;
USE school_management;

CREATE TABLE schools (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  address VARCHAR(500) NOT NULL,
  latitude FLOAT NOT NULL,
  longitude FLOAT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);`}
              </pre>
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <Code className="h-5 w-5" />
              Express.js Server Setup
            </h3>
            <div className="bg-gray-100 p-4 rounded-lg">
              <pre className="text-sm overflow-x-auto">
{`// package.json dependencies
{
  "express": "^4.18.2",
  "mysql2": "^3.6.0",
  "cors": "^2.8.5",
  "dotenv": "^16.3.1",
  "joi": "^17.9.2"
}

// server.js
const express = require('express');
const mysql = require('mysql2/promise');
const cors = require('cors');
const Joi = require('joi');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// Database connection
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'school_management'
};`}
              </pre>
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">Add School API Implementation</h3>
            <div className="bg-gray-100 p-4 rounded-lg">
              <pre className="text-sm overflow-x-auto">
{`// Validation schema
const schoolSchema = Joi.object({
  name: Joi.string().min(1).max(255).required(),
  address: Joi.string().min(1).max(500).required(),
  latitude: Joi.number().min(-90).max(90).required(),
  longitude: Joi.number().min(-180).max(180).required()
});

// POST /addSchool
app.post('/addSchool', async (req, res) => {
  try {
    // Validate input
    const { error, value } = schoolSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        details: error.details[0].message
      });
    }

    const { name, address, latitude, longitude } = value;

    // Create database connection
    const connection = await mysql.createConnection(dbConfig);

    // Insert school into database
    const [result] = await connection.execute(
      'INSERT INTO schools (name, address, latitude, longitude) VALUES (?, ?, ?, ?)',
      [name, address, latitude, longitude]
    );

    await connection.end();

    res.status(201).json({
      success: true,
      message: 'School added successfully',
      data: {
        id: result.insertId,
        name,
        address,
        latitude,
        longitude
      }
    });

  } catch (error) {
    console.error('Error adding school:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});`}
              </pre>
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">List Schools API Implementation</h3>
            <div className="bg-gray-100 p-4 rounded-lg">
              <pre className="text-sm overflow-x-auto">
{`// Helper function to calculate distance
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // Radius of Earth in kilometers
  const dLat = deg2rad(lat2 - lat1);
  const dLon = deg2rad(lon2 - lon1);
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const d = R * c; // Distance in km
  return Math.round(d * 100) / 100; // Round to 2 decimal places
}

function deg2rad(deg) {
  return deg * (Math.PI/180);
}

// GET /listSchools
app.get('/listSchools', async (req, res) => {
  try {
    const { latitude, longitude } = req.query;

    // Validate coordinates
    const lat = parseFloat(latitude);
    const lng = parseFloat(longitude);

    if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
      return res.status(400).json({
        success: false,
        message: 'Invalid latitude or longitude'
      });
    }

    // Create database connection
    const connection = await mysql.createConnection(dbConfig);

    // Fetch all schools
    const [schools] = await connection.execute('SELECT * FROM schools');

    await connection.end();

    // Calculate distance for each school and sort
    const schoolsWithDistance = schools.map(school => ({
      ...school,
      distance: calculateDistance(lat, lng, school.latitude, school.longitude)
    })).sort((a, b) => a.distance - b.distance);

    res.json({
      success: true,
      data: schoolsWithDistance,
      userLocation: { latitude: lat, longitude: lng }
    });

  } catch (error) {
    console.error('Error fetching schools:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});`}
              </pre>
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Server Startup & Environment
            </h3>
            <div className="bg-gray-100 p-4 rounded-lg">
              <pre className="text-sm overflow-x-auto">
{`// Complete server startup
app.listen(PORT, () => {
  console.log(\`Server running on port \${PORT}\`);
});

// .env file
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=school_management
PORT=3000

// Start server
node server.js`}
              </pre>
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">Postman Collection Examples</h3>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Add School Request:</h4>
                <div className="bg-gray-100 p-3 rounded">
                  <pre className="text-sm">
{`POST http://localhost:3000/addSchool
Content-Type: application/json

{
  "name": "Springfield Elementary School",
  "address": "123 Main St, Springfield",
  "latitude": 40.7128,
  "longitude": -74.0060
}`}
                  </pre>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">List Schools Request:</h4>
                <div className="bg-gray-100 p-3 rounded">
                  <pre className="text-sm">
{`GET http://localhost:3000/listSchools?latitude=40.7589&longitude=-73.9851`}
                  </pre>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          <div>
            <h3 className="text-lg font-semibold mb-3">Deployment Options</h3>
            <ul className="list-disc list-inside space-y-2 text-sm">
              <li><strong>Heroku:</strong> Easy deployment with MySQL add-ons</li>
              <li><strong>Railway:</strong> Simple Node.js deployment with database</li>
              <li><strong>DigitalOcean App Platform:</strong> Full-stack deployment</li>
              <li><strong>AWS EC2:</strong> Custom server setup with RDS</li>
              <li><strong>Vercel:</strong> Serverless functions for APIs</li>
            </ul>
          </div>

        </CardContent>
      </Card>
    </div>
  );
};

export default BackendDocumentation;
