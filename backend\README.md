# School Management Backend API

This is the Node.js backend server for the School Locator API Service. It provides REST APIs for managing schools and calculating distances.

## Features

- **Add School API**: POST `/addSchool` - Add new schools to the database
- **List Schools API**: GET `/listSchools` - Get schools sorted by distance from user location
- **Distance Calculation**: Automatic distance calculation using Haversine formula
- **Input Validation**: Comprehensive validation using Joi
- **CORS Support**: Cross-origin requests enabled for frontend integration
- **MySQL Database**: Persistent storage with MySQL

## Prerequisites

1. **Node.js** (v14 or higher)
2. **MySQL Server** (v5.7 or higher)
3. **npm** or **yarn** package manager

## Database Setup

1. **Create MySQL Database:**
```sql
CREATE DATABASE school_management;
USE school_management;

CREATE TABLE schools (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHA<PERSON>(255) NOT NULL,
  address VARCHAR(500) NOT NULL,
  latitude FLOAT NOT NULL,
  longitude FLOAT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

2. **Configure Database Connection:**
   - Update the `.env` file with your MySQL credentials
   - Default configuration assumes MySQL running on localhost with root user

## Installation & Setup

1. **Navigate to backend directory:**
```bash
cd backend
```

2. **Install dependencies:**
```bash
npm install
```

3. **Configure environment variables:**
   - Copy `.env.example` to `.env` (if needed)
   - Update database credentials in `.env` file

4. **Start the server:**
```bash
# Development mode with auto-restart
npm run dev

# Production mode
npm start
```

The server will start on `http://localhost:3000`

## API Endpoints

### POST /addSchool
Add a new school to the database.

**Request Body:**
```json
{
  "name": "Springfield Elementary School",
  "address": "123 Main St, Springfield",
  "latitude": 40.7128,
  "longitude": -74.0060
}
```

**Response:**
```json
{
  "id": 1,
  "name": "Springfield Elementary School",
  "address": "123 Main St, Springfield",
  "latitude": 40.7128,
  "longitude": -74.0060
}
```

### GET /listSchools
Get all schools sorted by distance from the provided coordinates.

**Query Parameters:**
- `latitude` (required): User's latitude
- `longitude` (required): User's longitude

**Example:**
```
GET /listSchools?latitude=40.7589&longitude=-73.9851
```

**Response:**
```json
[
  {
    "id": 1,
    "name": "Springfield Elementary School",
    "address": "123 Main St, Springfield",
    "latitude": 40.7128,
    "longitude": -74.0060,
    "distance": 5.23
  }
]
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_HOST` | MySQL host | localhost |
| `DB_USER` | MySQL username | root |
| `DB_PASSWORD` | MySQL password | (empty) |
| `DB_NAME` | Database name | school_management |
| `PORT` | Server port | 3000 |

## Testing

You can test the APIs using:
- **Postman** - Import the provided collection
- **curl** commands
- **Frontend application** - The React frontend is pre-configured to connect to this backend

## Troubleshooting

1. **Database Connection Issues:**
   - Verify MySQL is running
   - Check database credentials in `.env`
   - Ensure database and table exist

2. **Port Already in Use:**
   - Change PORT in `.env` file
   - Kill existing process on port 3000

3. **CORS Issues:**
   - CORS is enabled for all origins
   - Check if frontend is making requests to correct URL

## Development

- Server automatically restarts on file changes when using `npm run dev`
- Logs are printed to console for debugging
- Error handling includes detailed error messages
