
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MapPin, AlertCircle, CheckCircle2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface LocationHandlerProps {
  onLocationUpdate: (lat: number, lng: number) => void;
  currentLocation?: { lat: number; lng: number } | null;
}

const LocationHandler = ({ onLocationUpdate, currentLocation }: LocationHandlerProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState<'prompt' | 'granted' | 'denied' | null>(null);
  const { toast } = useToast();

  const requestLocation = async () => {
    setIsLoading(true);
    
    try {
      // Check if geolocation is supported
      if (!navigator.geolocation) {
        throw new Error("Geolocation is not supported by this browser");
      }

      // Check permission status if available
      if ('permissions' in navigator) {
        try {
          const permission = await navigator.permissions.query({ name: 'geolocation' });
          setPermissionStatus(permission.state);
          
          if (permission.state === 'denied') {
            toast({
              title: "Location Access Denied",
              description: "Please enable location access in your browser settings and refresh the page.",
              variant: "destructive",
            });
            setIsLoading(false);
            return;
          }
        } catch (permError) {
          console.log("Permission API not available:", permError);
        }
      }

      // Request current position with improved options
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          resolve,
          reject,
          {
            enableHighAccuracy: true,
            timeout: 10000, // 10 seconds
            maximumAge: 60000 // 1 minute cache
          }
        );
      });

      const { latitude, longitude } = position.coords;
      onLocationUpdate(latitude, longitude);
      setPermissionStatus('granted');
      
      toast({
        title: "Location Found!",
        description: `Coordinates: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
      });

    } catch (error: any) {
      console.error("Geolocation error:", error);
      
      let errorMessage = "Could not get your location. ";
      
      switch (error.code) {
        case 1: // PERMISSION_DENIED
          setPermissionStatus('denied');
          errorMessage += "Permission denied. Please enable location access in your browser.";
          break;
        case 2: // POSITION_UNAVAILABLE
          errorMessage += "Location information is unavailable.";
          break;
        case 3: // TIMEOUT
          errorMessage += "Location request timed out. Please try again.";
          break;
        default:
          errorMessage += error.message || "Unknown error occurred.";
      }
      
      toast({
        title: "Location Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      {permissionStatus === 'denied' && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            Location access is blocked. Please enable it in your browser settings and refresh the page.
          </AlertDescription>
        </Alert>
      )}
      
      {currentLocation && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            Current location: {currentLocation.lat.toFixed(6)}, {currentLocation.lng.toFixed(6)}
          </AlertDescription>
        </Alert>
      )}
      
      <Button
        onClick={requestLocation}
        disabled={isLoading || permissionStatus === 'denied'}
        className="w-full sm:w-auto bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-medium"
      >
        <MapPin className="h-4 w-4 mr-2" />
        {isLoading ? "Getting Location..." : "Use Current Location"}
      </Button>
    </div>
  );
};

export default LocationHandler;
