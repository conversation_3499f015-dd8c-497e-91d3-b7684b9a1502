
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, School as SchoolIcon, MapPin, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { schoolApiService } from "@/services/schoolApi";
import { School } from "@/types/school";
import LocationHandler from "./LocationHandler";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface AddSchoolFormProps {
  onSchoolAdded: (school: School) => void;
}

const AddSchoolForm = ({ onSchoolAdded }: AddSchoolFormProps) => {
  const [formData, setFormData] = useState({
    name: "",
    address: "",
    latitude: "",
    longitude: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [locationSet, setLocationSet] = useState(false);
  const { toast } = useToast();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      toast({
        title: "Validation Error",
        description: "School name is required",
        variant: "destructive",
      });
      return false;
    }

    if (!formData.address.trim()) {
      toast({
        title: "Validation Error", 
        description: "School address is required",
        variant: "destructive",
      });
      return false;
    }

    const lat = parseFloat(formData.latitude);
    const lng = parseFloat(formData.longitude);

    if (isNaN(lat) || lat < -90 || lat > 90) {
      toast({
        title: "Validation Error",
        description: "Please enter a valid latitude (-90 to 90)",
        variant: "destructive",
      });
      return false;
    }

    if (isNaN(lng) || lng < -180 || lng > 180) {
      toast({
        title: "Validation Error",
        description: "Please enter a valid longitude (-180 to 180)",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const handleLocationUpdate = (lat: number, lng: number) => {
    setFormData(prev => ({
      ...prev,
      latitude: lat.toString(),
      longitude: lng.toString(),
    }));
    setLocationSet(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);

    try {
      const schoolData = {
        name: formData.name.trim(),
        address: formData.address.trim(),
        latitude: parseFloat(formData.latitude),
        longitude: parseFloat(formData.longitude),
      };

      console.log("Attempting to add school:", schoolData);
      const newSchool = await schoolApiService.addSchool(schoolData);
      
      onSchoolAdded(newSchool);
      
      // Reset form
      setFormData({
        name: "",
        address: "",
        latitude: "",
        longitude: "",
      });
      setLocationSet(false);

      toast({
        title: "Success!",
        description: `${schoolData.name} has been added successfully`,
      });
    } catch (error: any) {
      console.error("Failed to add school:", error);
      
      toast({
        title: "Connection Error",
        description: "Could not connect to the server. Please ensure the backend is running on http://localhost:3001",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Alert className="border-blue-200 bg-blue-50">
        <AlertCircle className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          <strong>Backend Connected:</strong> Node.js server is running on http://localhost:3001 with in-memory database ready!
        </AlertDescription>
      </Alert>

      <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-gray-50">
        <CardHeader className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-t-lg">
          <CardTitle className="flex items-center gap-2 text-xl">
            <SchoolIcon className="h-6 w-6" />
            Add New School
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6 space-y-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium text-gray-700">
                  School Name *
                </Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Enter school name"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="address" className="text-sm font-medium text-gray-700">
                  Address *
                </Label>
                <Input
                  id="address"
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  placeholder="Enter school address"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="latitude" className="text-sm font-medium text-gray-700">
                  Latitude *
                </Label>
                <Input
                  id="latitude"
                  name="latitude"
                  type="number"
                  step="any"
                  value={formData.latitude}
                  onChange={handleInputChange}
                  placeholder="e.g., 40.7128"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="longitude" className="text-sm font-medium text-gray-700">
                  Longitude *
                </Label>
                <Input
                  id="longitude"
                  name="longitude"
                  type="number"
                  step="any"
                  value={formData.longitude}
                  onChange={handleInputChange}
                  placeholder="e.g., -74.0060"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  required
                />
              </div>
            </div>

            <div className="border-t pt-6">
              <h4 className="text-sm font-medium text-gray-700 mb-4 flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Get Current Location
              </h4>
              <LocationHandler 
                onLocationUpdate={handleLocationUpdate}
                currentLocation={locationSet ? { 
                  lat: parseFloat(formData.latitude), 
                  lng: parseFloat(formData.longitude) 
                } : null}
              />
            </div>

            <div className="pt-4">
              <Button
                type="submit"
                disabled={isLoading}
                className="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-medium py-3 text-lg"
              >
                <Plus className="h-5 w-5 mr-2" />
                {isLoading ? "Adding School..." : "Add School"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default AddSchoolForm;
